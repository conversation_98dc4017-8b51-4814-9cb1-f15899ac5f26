"""
测试部门层级解析功能
"""

import pytest
from unittest.mock import Mock, patch
from app.services.department_structure_sync import DepartmentStructureSyncService
from app.schemas.department_sync import DepartmentSyncRequest, DepartmentInfo, DepartmentSyncSource
from app.models.ecology_user import EcologyUser


class TestDepartmentHierarchy:
    """部门层级解析测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.mock_db = Mock()
        self.sync_service = DepartmentStructureSyncService(self.mock_db)
    
    @patch('app.crud.ecology_user.get_ecology_users')
    async def test_department_hierarchy_parsing(self, mock_get_users):
        """测试部门层级解析"""
        # 模拟用户数据
        mock_users = [
            Mock(
                dept_hierarchy="重庆至信实业股份有限公司（重庆至信） > 总经理办公室 > 业务中心",
                dept_name="业务中心",
                dept_id=100,
                company_id=1,
                company_name="重庆至信实业股份有限公司（重庆至信）",
                status="正式"
            ),
            Mock(
                dept_hierarchy="重庆至信实业股份有限公司（重庆至信） > 技术部",
                dept_name="技术部",
                dept_id=200,
                company_id=1,
                company_name="重庆至信实业股份有限公司（重庆至信）",
                status="正式"
            ),
            Mock(
                dept_hierarchy="另一家公司 > 业务中心",
                dept_name="业务中心",
                dept_id=300,
                company_id=2,
                company_name="另一家公司",
                status="正式"
            )
        ]
        
        mock_get_users.return_value = mock_users
        
        request = DepartmentSyncRequest(skip_empty_departments=False)
        
        # 执行部门数据获取
        departments = await self.sync_service._get_departments_from_ecology(request)
        
        # 验证结果
        assert len(departments) > 0
        
        # 检查是否正确解析了公司级别的部门
        company_depts = [d for d in departments if d.level == 1]
        assert len(company_depts) >= 2  # 应该有两个公司
        
        # 检查公司名称
        company_names = [d.dept_name for d in company_depts]
        assert "重庆至信实业股份有限公司（重庆至信）" in company_names
        assert "另一家公司" in company_names
        
        # 检查业务中心部门（应该有两个，来自不同公司）
        business_centers = [d for d in departments if d.dept_name == "业务中心"]
        assert len(business_centers) == 2
        
        # 验证它们属于不同的公司
        company_ids = [d.company_id for d in business_centers]
        assert 1 in company_ids
        assert 2 in company_ids
    
    def test_mapped_department_name_with_duplicates(self):
        """测试重名部门的名称映射"""
        # 准备测试数据
        dept1 = DepartmentInfo(
            dept_id=1,
            dept_name="业务中心",
            level=2,
            company_id=1,
            company_name="重庆至信实业股份有限公司（重庆至信）"
        )
        
        dept2 = DepartmentInfo(
            dept_id=2,
            dept_name="业务中心",
            level=2,
            company_id=2,
            company_name="另一家公司"
        )
        
        # 设置原始部门列表
        self.sync_service.original_departments = [dept1, dept2]
        
        # 测试第一个部门的映射名称
        mapped_name1 = self.sync_service._get_mapped_department_name("业务中心", 2, dept1)
        mapped_name2 = self.sync_service._get_mapped_department_name("业务中心", 2, dept2)
        
        # 验证结果 - 应该添加了公司前缀以区分重名
        assert "重庆至信实业股份有限" in mapped_name1
        assert "另一家公司" in mapped_name2

        # 两个映射后的名称应该不同
        assert mapped_name1 != mapped_name2
    
    def test_company_level_department_name(self):
        """测试公司级别部门的名称映射"""
        dept = DepartmentInfo(
            dept_id=1,
            dept_name="重庆至信实业股份有限公司（重庆至信）",
            level=1,
            company_id=1,
            company_name="重庆至信实业股份有限公司（重庆至信）"
        )
        
        self.sync_service.original_departments = [dept]
        
        # 公司级别的部门不应该添加前缀
        mapped_name = self.sync_service._get_mapped_department_name(
            "重庆至信实业股份有限公司（重庆至信）", 1, dept
        )
        
        # 由于存在默认的名称映射，带括号的公司名会被映射为不带括号的版本
        assert mapped_name == "重庆至信实业股份有限公司"


if __name__ == "__main__":
    pytest.main([__file__])
